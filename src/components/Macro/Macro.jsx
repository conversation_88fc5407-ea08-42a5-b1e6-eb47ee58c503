import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>ton, Card, Modal, message, Typography, Space, Tag, Select, Divider } from 'antd';
import { StopOutlined, DeleteOutlined } from '@ant-design/icons';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { setMacroConfig, encodeMacroData, parseMacroBuffer } from './SetMacro';
import { getHIDKeyCode, getKeyName } from './MacroKeyMap';
import './Macro.css';
import { findNameByCode } from '../../utils/hidUtils';

const { Title, Text } = Typography;
const { Option } = Select;

const Macro = () => {
  const { addToQueue, dataQueue, macro, setMacro } = useHandleDevice();
  const [isRecording, setIsRecording] = useState(false);
  const [currentMacro, setCurrentMacro] = useState(0); // 默认选中M0
  const [recordedActions, setRecordedActions] = useState([]);
  const [macroList, setMacroList] = useState([]);
  const recordStartTime = useRef(null);
  const lastActionTime = useRef(null);
  const [messageApi, contextHolder] = message.useMessage();

  // 初始化M0-M15宏键列表
  useEffect(() => {
    // 解析设备返回的宏数据
    const parsedActions = parseMacroBuffer(macro.buffer);
    // parsedActions 应为长度为16的数组，每个元素为 { actions: [...] }
    // 需要和M0-M15一一对应
    if (Array.isArray(parsedActions) && parsedActions.length === 16) {
      const macros = parsedActions.map((item, idx) => ({
        id: `M${idx}`,
        name: `M${idx}`,
        actions: Array.isArray(item.actions) ? item.actions : [],
        description: `宏键 ${idx}`
      }));
      setMacroList(macros);
    } else {
      // fallback: 保证有16个宏键
      const macros = [];
      for (let i = 0; i < 16; i++) {
        macros.push({
          id: `M${i}`,
          name: `M${i}`,
          actions: [],
          description: `宏键 ${i}`
        });
      }
      setMacroList(macros);
    }
  }, [macro]);

  // 监听键盘事件进行录制
  useEffect(() => {
    if (!isRecording) return;

    const handleKeyDown = (event) => {
      if (!isRecording) return;

      const now = Date.now();
      if (!recordStartTime.current) {
        recordStartTime.current = now;
        lastActionTime.current = now;
      }

      // 计算与上一个动作的时间间隔
      const timeSinceLastAction = now - lastActionTime.current;

      // 如果间隔大于10ms，添加延时动作
      if (timeSinceLastAction > 10 && recordedActions.length > 0) {
        setRecordedActions(prev => [...prev, {
          type: 'delay',
          duration: timeSinceLastAction,
          timestamp: now - recordStartTime.current
        }]);
      }

      // 添加按键按下动作
      const hidKeyCode = getHIDKeyCode(event.keyCode);
      if (hidKeyCode !== 0x00) {
        setRecordedActions(prev => [...prev, {
          type: 'keydown',
          keyCode: hidKeyCode,
          jsKeyCode: event.keyCode,
          key: getKeyName(event.keyCode),
          timestamp: now - recordStartTime.current
        }]);
      }

      lastActionTime.current = now;
      event.preventDefault();
    };

    const handleKeyUp = (event) => {
      if (!isRecording) return;

      const now = Date.now();
      const timeSinceLastAction = now - lastActionTime.current;

      // 如果间隔大于10ms，添加延时动作
      if (timeSinceLastAction > 10) {
        setRecordedActions(prev => [...prev, {
          type: 'delay',
          duration: timeSinceLastAction,
          timestamp: now - recordStartTime.current
        }]);
      }

      // 添加按键抬起动作
      const hidKeyCode = getHIDKeyCode(event.keyCode);
      if (hidKeyCode !== 0x00) {
        setRecordedActions(prev => [...prev, {
          type: 'keyup',
          keyCode: hidKeyCode,
          timestamp: now - recordStartTime.current
        }]);
      }

      lastActionTime.current = now;
      event.preventDefault();
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isRecording, recordedActions]);

  const startRecording = () => {
    setRecordedActions([]);
    setIsRecording(true);
    recordStartTime.current = null;
    lastActionTime.current = null;
    messageApi.info(`开始录制 M${currentMacro}，按任意键开始录制...`);
  };

  const stopRecording = () => {
    setIsRecording(false);
    if (recordedActions.length > 0) {
      // 保存录制的动作到对应的宏键
      setMacroList(prev => {
        const newList = [...prev];
        newList[currentMacro] = {
          ...newList[currentMacro],
          actions: recordedActions,
          description: `包含 ${recordedActions.filter(a => a.type === 'keydown').length} 个按键动作`
        };

        let allEncodedData = [];
        for (let macroIdx = 0; macroIdx <= 15; macroIdx++) {
          const actions = newList[macroIdx] && Array.isArray(newList[macroIdx].actions)
            ? newList[macroIdx].actions
            : [];
          let encodedData = encodeMacroData(actions);
          // 每个宏后面都加00收尾
          encodedData.push(0x00);
          allEncodedData = allEncodedData.concat(encodedData);
        }
        // 按照协议，整体偏移为0
        setMacroConfig(dataQueue, 0, allEncodedData);
        console.log('newList', newList);
        return newList;
      });
      messageApi.success(`M${currentMacro} 录制完成！`);
    } else {
      messageApi.warning('没有录制到任何动作');
    }
    setRecordedActions([]);
  };

  // 补充删除宏的逻辑
  const deleteMacro = (macroIndex) => {
    console.log('点击删除宏键', macroIndex);
    Modal.confirm({
      title: `确认删除 M${macroIndex}？`,
      content: '删除后将无法恢复',
      onOk: () => {
        setMacroList(prev => {
          const newList = [...prev];
          // 清空该宏的动作
          newList[macroIndex] = {
            ...newList[macroIndex],
            actions: [],
            description: `宏键 ${macroIndex}`
          };

          // 重新编码所有宏数据并写入设备
          let allEncodedData = [];
          for (let macroIdx = 0; macroIdx <= 15; macroIdx++) {
            const actions = newList[macroIdx] && Array.isArray(newList[macroIdx].actions)
              ? newList[macroIdx].actions
              : [];
            let encodedData = encodeMacroData(actions);
            // 每个宏后面都加00收尾
            encodedData.push(0x00);
            allEncodedData = allEncodedData.concat(encodedData);
          }
          setMacroConfig(dataQueue, 0, allEncodedData);
          return newList;
        });
        messageApi.success(`M${macroIndex} 已删除`);
      }
    });
  };

  const formatActionType = (action) => {
    switch (action.type) {
      case 'keydown':
        return <Tag color="green">按下 {findNameByCode(`00 ${Number(action.keyCode).toString(16).padStart(2, '0').toUpperCase()}`)}</Tag>;
      case 'keyup':
        return <Tag color="red">抬起 {findNameByCode(`00 ${Number(action.keyCode).toString(16).padStart(2, '0').toUpperCase()}`)}</Tag>;
      case 'delay':
        return <Tag color="blue">延时 {action.duration}ms</Tag>;
      default:
        return <Tag>{action.type}</Tag>;
    }
  };

  // 新增：直接显示按键动作的渲染函数
  const renderActionList = (actions) => {
    if (!actions || actions.length === 0) {
      return <Text type="secondary">暂无动作</Text>;
    }
    return (
      <div style={{ marginTop: 8, maxHeight: 200, overflow: 'auto', background: '#fafafa', borderRadius: 4, padding: 8 }}>
        {actions.map((action, idx) => (
          <div key={idx} style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
            {formatActionType(action)}
            <Text type="secondary" style={{ marginLeft: 8 }}>
              {action.timestamp}ms
            </Text>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="macro-container">
      {contextHolder}
      <div style={{width: '100%', maxWidth: '1120px', marginTop: '2em'}}>
        <div className="d-flex align-items-center" style={{justifyContent: 'space-between'}}>
          <div className="d-flex align-items-center">
            <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
            <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>宏键录制</div>
          </div>
        </div>
      </div>
      <div>list</div>
      {/* <div>{JSON.stringify(macroList)}</div> */}

      <div className="macro-content">
        <div className="macro-list" style={{maxWidth: 400, margin: '0 auto'}}>
          <Title level={4}>选择宏键 (M0-M15)</Title>
          <Space direction="vertical" style={{width: '100%'}}>
            <Select
              style={{ width: '100%' }}
              value={currentMacro}
              onChange={value => setCurrentMacro(value)}
              disabled={isRecording}
            >
              {macroList.map((item, idx) => (
                <Option key={item.id} value={idx}>{item.name}</Option>
              ))}
            </Select>
            <Card
              title={macroList[currentMacro]?.name}
              size="small"
              actions={[
                <Button
                  key="record"
                  type={isRecording ? "primary" : "default"}
                  icon={isRecording ? <StopOutlined /> : null}
                  onClick={() => {
                    if (isRecording) {
                      stopRecording();
                    } else {
                      startRecording();
                    }
                  }}
                >
                  {isRecording ? '停止' : '录制'}
                </Button>,
                <Button
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => deleteMacro(currentMacro)}
                  disabled={macroList[currentMacro]?.actions.length === 0 || isRecording}
                >
                  删除
                </Button>
              ]}
            >
              <Text type="secondary">{macroList[currentMacro]?.description}</Text>
              {macroList[currentMacro]?.actions.length > 0 && (
                <>
                  <div style={{marginTop: '8px'}}>
                    <Text strong>动作数: {macroList[currentMacro]?.actions.length}</Text>
                  </div>
                  <Divider style={{margin: '8px 0'}} />
                  {/* 直接显示动作列表 */}
                  {renderActionList(macroList[currentMacro]?.actions)}
                </>
              )}
            </Card>
          </Space>
        </div>

        {isRecording && (
          <div className="recording-panel">
            <Card title={`正在录制 M${currentMacro}`} style={{marginTop: '20px'}}>
              <Space direction="vertical" style={{width: '100%'}}>
                <Text>已录制 {recordedActions.length} 个动作</Text>
                {/* 直接显示录制中的动作列表 */}
                {renderActionList(recordedActions)}
                <Button type="primary" danger onClick={stopRecording}>
                  停止录制
                </Button>
              </Space>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default Macro;
